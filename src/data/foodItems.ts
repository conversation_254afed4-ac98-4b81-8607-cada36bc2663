import { FoodItem } from "@/types/food";

export const sideItems: FoodItem[] = [
  { id: 'side-1', name: 'キャベツ', price: 30, type: 'side' },
  { id: 'side-2', name: 'ポテト サラダ', price: 100, type: 'side' },
  { id: 'side-3', name: 'みそ汁', price: 100, type: 'side' },
  { id: 'side-4', name: 'ごはん（小）', price: 50, type: 'side' },
  { id: 'side-5', name: '唐揚げ パック', price: 100, type: 'side' },
  { id: 'side-6', name: 'サラダ', price: 80, type: 'side' },
  { id: 'side-7', name: 'キムチ', price: 60, type: 'side' },
  { id: 'side-8', name: 'だし巻き卵', price: 90, type: 'side' },
  { id: 'side-9', name: 'つけもの', price: 40, type: 'side' },
  { id: 'side-10', name: 'もやし炒め', price: 70, type: 'side' },
];

export const mainItems: FoodItem[] = [
  { id: 'main-1', name: '唐揚げ', price: 400, type: 'main' },
  { id: 'main-2', name: 'のり (Thập Cẩm - Nhỏ)', price: 400, type: 'main' },
  { id: 'main-3', name: 'そぼろ', price: 400, type: 'main' },
  { id: 'main-4', name: 'ハンバーグ', price: 440, type: 'main' },
  { id: 'main-5', name: 'メンチカツ', price: 440, type: 'main' },
  { id: 'main-6', name: '生姜焼き', price: 440, type: 'main' },
  { id: 'main-7', name: 'とんかつ', price: 440, type: 'main' },
  { id: 'main-8', name: '焼き肉', price: 440, type: 'main' },
  { id: 'main-9', name: '焼き魚', price: 440, type: 'main' },
  { id: 'main-10', name: 'カキフライ', price: 440, type: 'main' },
  { id: 'main-11', name: 'スポーツ (Thập Cẩm - Lớn)', price: 500, type: 'main' },
];

export const allFoodItems = [...sideItems, ...mainItems];