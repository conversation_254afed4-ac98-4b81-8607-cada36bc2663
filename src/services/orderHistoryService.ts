import { supabase } from "@/integrations/supabase/client";
import { Database, TablesInsert } from "@/integrations/supabase/types";

// Type aliases for better readability
type OrderHistoryInsert = TablesInsert<"order_history">;
type OrderHistoryRow = Database["public"]["Tables"]["order_history"]["Row"];

export type OrderHistoryActionType = "order_placed" | "item_removed" | "order_cancelled";

export interface OrderHistoryEvent {
  id: string;
  userName: string;
  actionType: OrderHistoryActionType;
  orderId?: string;
  itemName?: string;
  itemQuantity?: number;
  totalAmount?: number;
  description: string;
  createdAt: string;
}

export interface AddHistoryEventParams {
  userName: string;
  actionType: OrderHistoryActionType;
  description: string;
  orderId?: string;
  itemName?: string;
  itemQuantity?: number;
  totalAmount?: number;
}

export interface GetHistoryResult {
  success: boolean;
  events?: OrderHistoryEvent[];
  error?: string;
}

export interface AddHistoryResult {
  success: boolean;
  event?: OrderHistoryEvent;
  error?: string;
}

/**
 * Service for managing order history events
 * Supports Supabase database only
 */
export const orderHistoryService = {
  /**
   * Add a new history event
   */
  async addHistoryEvent(params: AddHistoryEventParams): Promise<AddHistoryResult> {
    try {
      // Try to save to Supabase first
      const historyInsert: OrderHistoryInsert = {
        user_name: params.userName,
        action_type: params.actionType,
        description: params.description,
        order_id: params.orderId || null,
        item_name: params.itemName || null,
        item_quantity: params.itemQuantity || null,
        total_amount: params.totalAmount || null,
      };

      const { data: newEvent, error } = await supabase
        .from("order_history")
        .insert(historyInsert)
        .select()
        .single();

      if (error) {
        console.warn("Failed to save to Supabase, falling back to localStorage:", error);
      }


      const event: OrderHistoryEvent = {
        id: newEvent.id,
        userName: newEvent.user_name,
        actionType: newEvent.action_type as OrderHistoryActionType,
        orderId: newEvent.order_id || undefined,
        itemName: newEvent.item_name || undefined,
        itemQuantity: newEvent.item_quantity || undefined,
        totalAmount: newEvent.total_amount || undefined,
        description: newEvent.description,
        createdAt: newEvent.created_at || new Date().toISOString(),
      };

      console.log("✅ History event saved to Supabase:", event);
      return { success: true, event };
    } catch (error) {
      console.error("❌ Error saving history event:", error);
    }
  },

  /**
   * Get history events for a specific user
   */
  async getHistoryForUser(userName: string): Promise<GetHistoryResult> {
    try {
      // Try to fetch from Supabase first
      const { data: events, error } = await supabase
        .from("order_history")
        .select("*")
        .eq("user_name", userName)
        .is("deleted_at", null)
        .order("created_at", { ascending: false });

      if (error) {
        console.warn("Failed to fetch from Supabase, falling back to localStorage:", error);
      }

      const transformedEvents: OrderHistoryEvent[] = (events || []).map(event => ({
        id: event.id,
        userName: event.user_name,
        actionType: event.action_type as OrderHistoryActionType,
        orderId: event.order_id || undefined,
        itemName: event.item_name || undefined,
        itemQuantity: event.item_quantity || undefined,
        totalAmount: event.total_amount || undefined,
        description: event.description,
        createdAt: event.created_at || new Date().toISOString(),
      }));

      console.log(`✅ Fetched ${transformedEvents.length} history events for ${userName}`);
      return { success: true, events: transformedEvents };
    } catch (error) {
      console.error("❌ Error fetching history events:", error);
    }
  },


  /**
   * Generate formatted description for different action types
   */
  generateDescription(actionType: OrderHistoryActionType, userName: string, itemName?: string): string {
    const now = new Date();
    const timeString = now.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });

    switch (actionType) {
      case "order_placed":
        return `${userName} đã đặt món lúc ${timeString}`;
      case "item_removed":
        return `${userName} đã xoá món ${itemName || 'không xác định'} lúc ${timeString}`;
      case "order_cancelled":
        return `${userName} đã huỷ đặt món lúc ${timeString}`;
      default:
        return `${userName} đã thực hiện hành động lúc ${timeString}`;
    }
  },
};
